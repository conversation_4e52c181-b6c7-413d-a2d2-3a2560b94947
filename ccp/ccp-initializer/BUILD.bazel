# Code generated by AKSBuilder. DO NOT EDIT.
load("@gazelle//:def.bzl", "gazelle")
load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library", "go_test")
load("@rules_pkg//pkg:mappings.bzl", "pkg_files", "strip_prefix")
load("@rules_pkg//pkg:pkg.bzl", "pkg_tar", "pkg_zip")
load("//:images.bzl", "container_images")

# gazelle:exclude */vendor/*
# gazelle:prefix go.goms.io/aks/rp/ccp/ccp-initializer
# gazelle:go_generate_proto false
gazelle(
    name = "gazelle",
    prefix = "go.goms.io/aks/rp/ccp/ccp-initializer",
)

go_binary(
    name = "ccp-initializer-linux-amd64",
    embed = ["//:ccp-initializer_lib"],
    goarch = "amd64",
    goos = "linux",
    pure = "on",
)

go_binary(
    name = "ccp-initializer-linux-arm64",
    embed = ["//:ccp-initializer_lib"],
    goarch = "arm64",
    goos = "linux",
    pure = "on",
)

container_images()

##### EV2 Images Starts #####

genrule(
    name = "ccp-initializer.image.ev2",
    srcs = [
        "//:aksservicemanifest.yaml",
        "//:all.image.digests.tar",
        "//:ccp-initializer-linux-amd64-image.tar",
        "//:ccp-initializer-linux-arm64-image.tar",
    ],
    outs = [
        "ccp-initializer.image.ev2.tar.gz",
    ],
    cmd =
        "$(location @rules_aksbuilder//:aksbuilder) " +
        "ev2 image " +
        "--name ccp-initializer " +  # this will used to match the one in the aksservicemanifest.yaml, and get the information defined there.
        "--linux-amd64-imagetar $(location :ccp-initializer-linux-amd64-image.tar) " +
        "--linux-arm64-imagetar $(location :ccp-initializer-linux-arm64-image.tar) " +
        "--imagedigests $(location :all.image.digests.tar) " +
        "--servicemanifest $(location :aksservicemanifest.yaml) " +
        "--aksbuilder $(location @rules_aksbuilder//:aksbuilder-linux-amd64) " +
        "--manifesttool $(location @manifesttool//file:manifesttool) " +
        "--orasPath $(location @oras_archive//:oras_binary) " +
        "--out $(location ccp-initializer.image.ev2.tar.gz)",
    tags = [
        "ev2",
        "image",
    ],
    tools = [
        "@manifesttool//file:manifesttool",
        "@oras_archive//:oras_binary",
        "@rules_aksbuilder//:aksbuilder",
        "@rules_aksbuilder//:aksbuilder-linux-amd64",
    ],
    visibility = ["//visibility:public"],
)

pkg_tar(
    name = "ccp-initializer.imagetar",
    srcs = [
        "//:ccp-initializer-linux-amd64-image.tar",
        "//:ccp-initializer-linux-arm64-image.tar",
    ],
    mode = "0777",
    tags = [
        "image",
    ],
)
##### EV2 Images Ends #####

go_library(
    name = "ccp-initializer_lib",
    srcs = ["main.go"],
    importpath = "go.goms.io/aks/rp/ccp/ccp-initializer",
    visibility = ["//visibility:private"],
    deps = [
        "//pkg",
        "//pkg/openvpn",
        "//pkg/tun",
        "@com_github_sirupsen_logrus//:logrus",
        "@com_github_spf13_cobra//:cobra",
        "@io_goms_go_aks_rp_toolkit_basicutils//timing",
        "@io_goms_go_aks_rp_toolkit_cmdexecutor//:cmdexecutor",
    ],
)

go_binary(
    name = "ccp-initializer",
    embed = [":ccp-initializer_lib"],
    visibility = ["//visibility:public"],
)

go_test(
    name = "ccp-initializer_test",
    srcs = ["ccp_initializer_suite_test.go"],
    deps = [
        "@com_github_onsi_ginkgo//:ginkgo",
        "@com_github_onsi_gomega//:gomega",
    ],
)
