###############################################################################
# <PERSON><PERSON> now uses Bzlmod by default to manage external dependencies.
# Please consider migrating your external dependencies from WORKSPACE to MODULE.bazel.
#
# For more details, please check https://github.com/bazelbuild/bazel/issues/18958
###############################################################################

module(
    name = "ccp-initializer",
    version = "0.0.1",
)

##### External Dependencies Starts #####
bazel_dep(name = "aspect_bazel_lib", version = "2.8.1")
archive_override(
    module_name = "aspect_bazel_lib",
    integrity = "sha256-aINU7mvuunGUJD1z6wmSuaEujt7u7FtlRPS1MaMRIjc=",
    strip_prefix = "bazel-lib-2.8.1",
    urls = [
        "https://artifprodeastus.z13.web.core.windows.net/bazel/lib/bazel-lib-v2.8.1.tar.gz",
        "https://github.com/bazel-contrib/bazel-lib/releases/download/v2.8.1/bazel-lib-v2.8.1.tar.gz",
    ],
)

bazel_dep(name = "bazel_skylib", version = "1.7.1")
archive_override(
    module_name = "bazel_skylib",
    integrity = "sha256-vCg8381SalLDIBJ5zaS8KYZS76iYsQtNsIN9xRZSdW8=",
    urls = [
        "https://artifprodeastus.z13.web.core.windows.net/bazel/skylib/bazel-skylib-1.7.1.tar.gz",
        "https://github.com/bazelbuild/bazel-skylib/releases/download/1.7.1/bazel-skylib-1.7.1.tar.gz",
    ],
)

bazel_dep(name = "container_structure_test", version = "1.19.1")
archive_override(
    module_name = "container_structure_test",
    integrity = "sha256-tWpT+3c0+TIWtg+M3TuY+712fp9BLAYdT6R5jlecSXE=",
    strip_prefix = "container-structure-test-1.19.1",
    urls = [
        "https://artifprodeastus.z13.web.core.windows.net/container-structure-test/v1.19.1.tar.gz",
        "https://github.com/GoogleContainerTools/container-structure-test/archive/refs/tags/v1.19.1.tar.gz",
    ],
)

bazel_dep(name = "gazelle", version = "0.38.0")
archive_override(
    module_name = "gazelle",
    integrity = "sha256-itd1UoJbB4oQrZYL7G73fS/47HD67y/QONtxP0EPXYc=",
    urls = [
        "https://artifprodeastus.z13.web.core.windows.net/bazel/gazelle/bazel-gazelle-v0.38.0.tar.gz",
        "https://github.com/bazel-contrib/bazel-gazelle/releases/download/v0.38.0/bazel-gazelle-v0.38.0.tar.gz",
    ],
)

bazel_dep(name = "rules_go", version = "0.49.0", repo_name = "io_bazel_rules_go")
archive_override(
    module_name = "rules_go",
    integrity = "sha256-2T7wLx5yyC2Ls9UWlRmzYWezPPaMJSUl47nT1d0UPec=",
    urls = [
        "https://artifprodeastus.z13.web.core.windows.net/bazel/rules/go/rules_go-v0.49.0.zip",
        "https://github.com/bazel-contrib/rules_go/releases/download/v0.49.0/rules_go-v0.49.0.zip",
    ],
)

bazel_dep(name = "rules_oci", version = "2.0.0")
archive_override(
    module_name = "rules_oci",
    integrity = "sha256-0AfmyW62LIg5e2jzKeTKVuDP4xIEosVLDLF4Gfifg8g=",
    strip_prefix = "rules_oci-2.0.0",
    urls = [
        "https://artifprodeastus.z13.web.core.windows.net/bazel/rules/oci/rules_oci-v2.0.0.tar.gz",
        "https://github.com/bazel-contrib/rules_oci/releases/download/v2.0.0/rules_oci-v2.0.0.tar.gz",
    ],
)

bazel_dep(name = "rules_pkg", version = "1.0.1")
archive_override(
    module_name = "rules_pkg",
    integrity = "sha256-0gyVGWDtd8t7NBwqWUiFNOSU1a0dMMSBjHNtV3cqn+8=",
    urls = [
        "https://artifprodeastus.z13.web.core.windows.net/bazel/rules/pkg/rules_pkg-1.0.1.tar.gz",
        "https://github.com/bazelbuild/rules_pkg/releases/download/1.0.1/rules_pkg-1.0.1.tar.gz",
    ],
)

bazel_dep(name = "rules_python", version = "0.35.0")
archive_override(
    module_name = "rules_python",
    integrity = "sha256-vgS2Nce+RgS+HvIFQumHCvPEl3jOhB7i2S/LQvnZUWo=",
    strip_prefix = "rules_python-0.35.0",
    urls = [
        "https://artifprodeastus.z13.web.core.windows.net/bazel/rules/python/rules_python-0.35.0.tar.gz",
        "https://github.com/bazelbuild/rules_python/releases/download/0.35.0/rules_python-0.35.0.tar.gz",
    ],
)

### Indirect Dependencies Starts ###
# bazel_dep(name = "abseil-cpp", version = "20240722.0")
archive_override(
    module_name = "abseil-cpp",
    integrity = "sha256-9Q5awxGoE4Laf6dblzEOS5AGR0+VYKxG9UqZZ/B9SuM=",
    strip_prefix = "abseil-cpp-20240722.0",
    urls = [
        "https://artifprodeastus.z13.web.core.windows.net/bazel/abseil-cpp/abseil-cpp-20240722.0.tar.gz",
        "https://github.com/abseil/abseil-cpp/releases/download/20240722.0/abseil-cpp-20240722.0.tar.gz",
    ],
)

# bazel_dep(name = "apple_support", version = "1.5.0")
archive_override(
    module_name = "apple_support",
    integrity = "sha256-miM41vja0yRPgj8txghKA+TQ+7J8qJLclw5okNW0gYQ=",
    urls = [
        "https://artifprodeastus.z13.web.core.windows.net/bazel/apple_support/apple_support.1.5.0.tar.gz",
        "https://github.com/bazelbuild/apple_support/releases/download/1.5.0/apple_support.1.5.0.tar.gz",
    ],
)

# bazel_dep(name = "bazel_features", version = "1.11.0")
archive_override(
    module_name = "bazel_features",
    integrity = "sha256-LNnlfUw4Z10yFzHWXBUljzpmQ4rVMa4Jy4uxQhfchXI=",
    strip_prefix = "bazel_features-1.11.0",
    urls = [
        "https://artifprodeastus.z13.web.core.windows.net/bazel/features/bazel_features-v1.11.0.tar.gz",
        "https://github.com/bazel-contrib/bazel_features/releases/download/v1.11.0/bazel_features-v1.11.0.tar.gz",
    ],
)

# bazel_dep(name = "buildozer", version = "7.1.2")
archive_override(
    module_name = "buildozer",
    integrity = "sha256-/HfDfwjmdFCKV5jYSRiuUrO5UQGZ4KwBESo3iWfaQVg=",
    strip_prefix = "buildozer-7.1.2",
    urls = [
        "https://artifprodeastus.z13.web.core.windows.net/bazel/buildozer/buildozer-v7.1.2.tar.gz",
        "https://github.com/fmeum/buildozer/releases/download/v7.1.2/buildozer-v7.1.2.tar.gz",
    ],
)

# bazel_dep(name = "googletest", version = "1.15.2")
archive_override(
    module_name = "googletest",
    integrity = "sha256-e0K01u1IgQxTYsJloX+uvpDcI3PIheUhZDnTeSfwKSY=",
    strip_prefix = "googletest-1.15.2",
    urls = [
        "https://artifprodeastus.z13.web.core.windows.net/bazel/googletest/googletest-1.15.2.tar.gz",
        "https://github.com/google/googletest/releases/download/v1.15.2/googletest-1.15.2.tar.gz",
    ],
)

# bazel_dep(name = "platforms", version = "0.0.10")
archive_override(
    module_name = "platforms",
    integrity = "sha256-IY7+juc20mo1cmY7N0olPAErcW2K8MB+hC6C8jigp+4=",
    urls = [
        "https://artifprodeastus.z13.web.core.windows.net/bazel/platforms/platforms-0.0.10.tar.gz",
        "https://github.com/bazelbuild/platforms/releases/download/0.0.10/platforms-0.0.10.tar.gz",
    ],
)

# bazel_dep(name = "protobuf", version = "27.0")
archive_override(
    module_name = "protobuf",
    integrity = "sha256-PhFI2wkP8hImwYiO85+nvHeQBCviH/Qon9Ic4XNfNFU=",
    strip_prefix = "protobuf-27.0",
    urls = [
        "https://artifprodeastus.z13.web.core.windows.net/bazel/protobuf/protobuf-27.0.zip",
        "https://github.com/protocolbuffers/protobuf/releases/download/v27.0/protobuf-27.0.zip",
    ],
)

# bazel_dep(name = "jsoncpp", version = "1.9.5")
archive_override(
    module_name = "jsoncpp",
    integrity = "sha256-yHpUH8SC4CCLLa08cCpB2JCdQ5WuKIwKqwVn1YuFVas=",
    strip_prefix = "jsoncpp-1.9.6",
    urls = [
        "https://artifprodeastus.z13.web.core.windows.net/bazel/jsoncpp/jsoncpp-1.9.6-patch.e4bcf64bb00107124af427bda61b9466532d758d.tar.gz",
        "https://artifprodsoutheastasia.z23.web.core.windows.net/bazel/jsoncpp/jsoncpp-1.9.6-patch.e4bcf64bb00107124af427bda61b9466532d758d.tar.gz",
    ],
)

# bazel_dep(name = "pybind11_bazel", version = "2.12.0")
archive_override(
    module_name = "pybind11_bazel",
    integrity = "sha256-pYwlxf4GOnAFf6IMuOFfO9oZsQMDBby1M68eRfNqSlU=",
    strip_prefix = "pybind11_bazel-2.12.0",
    urls = [
        "https://artifprodeastus.z13.web.core.windows.net/bazel/pybind11_bazel/pybind11_bazel-2.12.0.zip",
        "https://github.com/pybind/pybind11_bazel/releases/download/v2.12.0/pybind11_bazel-2.12.0.zip",
    ],
)

# bazel_dep(name = "re2", version = "2024-07-02")
archive_override(
    module_name = "re2",
    integrity = "sha256-qDX+Vfvc2OgPOFhKsi0IQGYsZ/L+s2vWeUAtqWQdxx4=",
    strip_prefix = "re2-2024-07-02",
    urls = [
        "https://artifprodeastus.z13.web.core.windows.net/bazel/google/re2/re2-2024-07-02.zip",
        "https://github.com/google/re2/releases/download/2024-07-02/re2-2024-07-02.zip",
    ],
)

# bazel_dep(name = "rules_cc", version = "0.0.17")
archive_override(
    module_name = "rules_cc",
    integrity = "sha256-DgjtjrIgC9ytYzcBBKkJv6YDgfySGuZvhDOeDwPAT90=",
    strip_prefix = "rules_cc-0.0.17",
    urls = [
        "https://artifprodeastus.z13.web.core.windows.net/bazel/rules/cc/rules_cc-0.0.17-patch.1743808b2d19637cf33ff3799ea1ceec2bc553ce.tar.gz",
        "https://artifprodsoutheastasia.z23.web.core.windows.net/bazel/rules/cc/rules_cc-0.0.17-patch.1743808b2d19637cf33ff3799ea1ceec2bc553ce.tar.gz",
    ],
)

# bazel_dep(name = "rules_java", version = "7.6.5")
archive_override(
    module_name = "rules_java",
    integrity = "sha256-iv0FPdKnuFpPAzWE8wp/FmbFSSxWx24E7sRCi9sqhs8=",
    urls = [
        "https://artifprodeastus.z13.web.core.windows.net/bazel/rules/java/rules_java-7.6.5.tar.gz",
        "https://github.com/bazelbuild/rules_java/releases/download/7.6.5/rules_java-7.6.5.tar.gz",
    ],
)

# bazel_dep(name = "rules_jvm_external", version = "5.2")
archive_override(
    module_name = "rules_jvm_external",
    integrity = "sha256-+G/UKoCeGHHKCqvonbDUQEUSGcPORsWNokDH3NwAEl8=",
    strip_prefix = "rules_jvm_external-5.2",
    urls = [
        "https://artifprodeastus.z13.web.core.windows.net/bazel/rules/jvm_external/rules_jvm_external-5.2.tar.gz",
        "https://github.com/bazelbuild/rules_jvm_external/releases/download/5.2/rules_jvm_external-5.2.tar.gz",
    ],
)

# bazel_dep(name = "rules_license", version = "0.0.7")
archive_override(
    module_name = "rules_license",
    integrity = "sha256-RTHezLkTY5ww5cdRKgVNXYdWmNrrddjPkPKEN1/nw2A=",
    urls = [
        "https://artifprodeastus.z13.web.core.windows.net/bazel/rules/license/rules_license-0.0.7.tar.gz",
        "https://github.com/bazelbuild/rules_license/releases/download/0.0.7/rules_license-0.0.7.tar.gz",
    ],
)

# bazel_dep(name = "rules_proto", version = "6.0.0")
archive_override(
    module_name = "rules_proto",
    integrity = "sha256-4EngY5EabtYXUmnQBRd2KUhRqUneOerMF0CZSZM1t4s=",
    strip_prefix = "rules_proto-6.0.0",
    urls = [
        "https://artifprodeastus.z13.web.core.windows.net/bazel/rules/proto/rules_proto-6.0.0-patch.1f5124643d2aeb81f8f69b8e05947f8acc62ae06.tar.gz",
        "https://artifprodsoutheastasia.z23.web.core.windows.net/bazel/rules/proto/rules_proto-6.0.0-patch.1f5124643d2aeb81f8f69b8e05947f8acc62ae06.tar.gz",
    ],
)

# bazel_dep(name = "stardoc", version = "0.6.2")
archive_override(
    module_name = "stardoc",
    integrity = "sha256-Yr0uYCFrem/sOseTQaogHglWR358j2zMKG8nmtHZZDI=",
    urls = [
        "https://artifprodeastus.z13.web.core.windows.net/bazel/stardoc/stardoc-0.6.2.tar.gz",
        "https://github.com/bazelbuild/stardoc/releases/download/0.6.2/stardoc-0.6.2.tar.gz",
    ],
)

# bazel_dep(name = "zlib", version = "1.3.1")
archive_override(
    module_name = "zlib",
    integrity = "sha256-XbhATtXyX6skRy4vlginxzj+TWAvhSqyyNlZhZkDGeU=",
    strip_prefix = "zlib-1.3.1",
    urls = [
        "https://artifprodeastus.z13.web.core.windows.net/bazel/zlib/zlib-1.3.1-patch.6217b9d57185ccb564d6ed03106d8bcd4a8bcb66.tar.gz",
        "https://artifprodsoutheastasia.z23.web.core.windows.net/bazel/zlib/zlib-1.3.1-patch.6217b9d57185ccb564d6ed03106d8bcd4a8bcb66.tar.gz",
    ],
)
### Indirect Dependencies Ends ###
##### External Dependencies Ends #####

# python override the "ignore_root_user_error" params
python = use_extension("@rules_python//python/extensions:python.bzl", "python")
python.toolchain(
    ignore_root_user_error = True,
    python_version = "3.11",
)

##### Kustomize Starts #####
bazel_dep(name = "rules_kustomize", version = "0.0.0")
local_path_override(
    module_name = "rules_kustomize",
    path = "../../devinfraservices/aksbuilder/bzlmod_rules/rules_kustomize",
)
##### Kustomize Ends #####

kustomize = use_extension("@rules_kustomize//kustomize:extensions.bzl", "kustomize")
kustomize.download(version = "v5.4.3")

##### rules_aksbuilder #####
bazel_dep(name = "rules_aksbuilder", version = "0.0.0")
local_path_override(
    module_name = "rules_aksbuilder",
    path = "../../devinfraservices/aksbuilder/bzlmod_rules/rules_aksbuilder",
)
##### rules_aksbuilder Ends #####

# golang
go_sdk = use_extension("@io_bazel_rules_go//go:extensions.bzl", "go_sdk")

# Download an SDK for the host OS & architecture as well as common remote execution platforms.
go_sdk.download(version = "1.23.9")

# go module
# please set the isolate = True if this module is not be used as root module
go_deps = use_extension("@gazelle//:extensions.bzl", "go_deps")
go_deps.from_file(go_mod = "//:go.mod")

# the go code of the protos are all generated manually
go_deps.gazelle_default_attributes(
    build_file_generation = "on",
    directives = [
        "gazelle:proto disable",
    ],
)

# use an empty repo and the bzl mod tidy will update this
use_repo(
    go_deps,
    "com_github_fsnotify_fsnotify",
    "com_github_onsi_ginkgo",
    "com_github_onsi_gomega",
    "com_github_sirupsen_logrus",
    "com_github_spf13_cobra",
    "com_github_stretchr_testify",
    "io_goms_go_aks_rp_toolkit_basicutils",
    "io_goms_go_aks_rp_toolkit_cmdexecutor",
    "io_goms_go_aks_rp_toolkit_httpx",
    "io_goms_go_aks_rp_toolkit_rand",
    "io_k8s_api",
    "io_k8s_apimachinery",
    "io_k8s_client_go",
    "org_uber_go_mock",
)

##### External Images Starts #####
oci = use_extension("@rules_oci//oci:extensions.bzl", "oci")
oci.pull(
    name = "base-os-runtime-nettools-linux-amd64",
    # 'tag' is also supported, but digest is encouraged for reproducibility.
    digest = "sha256:a93d0418c98cc862669ec3493d904de33fa3d21bca347b00c0d9ceedd4c0099d",
    image = "mcr.microsoft.com/aks/devinfra/base-os-runtime-nettools",
)
oci.pull(
    name = "base-os-runtime-nettools-linux-arm64",
    # 'tag' is also supported, but digest is encouraged for reproducibility.
    digest = "sha256:f2f3c40d72d6985d3aa8639f64f408c862b382f6c69c37ebfe1d06b929df81f6",
    image = "mcr.microsoft.com/aks/devinfra/base-os-runtime-nettools",
)
use_repo(
    oci,
    "base-os-runtime-nettools-linux-amd64",
    "base-os-runtime-nettools-linux-arm64",
)
##### External Images Ends #####

########### external binary starts ###############
http_file = use_repo_rule("@bazel_tools//tools/build_defs/repo:http.bzl", "http_file")

http_archive = use_repo_rule("@bazel_tools//tools/build_defs/repo:http.bzl", "http_archive")

http_file(
    name = "manifesttool",
    downloaded_file_path = "manifesttool",
    executable = True,
    sha256 = "8866eae2bf3db1c11309bd2d1bf6d72bc811789dfcc8fbb5afec3c395bd24425",
    urls = [
        "https://artifprodeastus.z13.web.core.windows.net/manifesttool/manifest-tool-linux-amd64-v2.0.3",
    ],
)

http_archive(
    name = "oras_archive",
    build_file_content = """
genrule(
  name = "gen-oras_amd64",
  srcs = glob(["**/*"]),
  outs = [
    "oras_binary",
  ],
  executable = True,
  cmd = "cp external/_main~_repo_rules~oras_archive/oras $(location oras_binary);",
  visibility = ["//visibility:public"],
)
""",
    sha256 = "8533c9ea1e5a0d5eb1dfc5094c0e8ef106d15462f8a119077548f88937ed2133",
    urls = [
        "https://artifprodeastus.z13.web.core.windows.net/oras/oras_1.0.0_linux_amd64.tar.gz",
        "https://github.com/oras-project/oras/releases/download/v1.0.0/oras_1.0.0_linux_amd64.tar.gz",
    ],
)
############# external binary ends #################
