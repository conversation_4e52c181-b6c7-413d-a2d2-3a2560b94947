{"lockFileVersion": 11, "registryFileHashes": {}, "selectedYankedVersions": {}, "moduleExtensions": {"@@apple_support~//crosstool:setup.bzl%apple_cc_configure_extension": {"general": {"bzlTransitiveDigest": "PjIds3feoYE8SGbbIq2SFTZy3zmxeO2tQevJZNDo7iY=", "usagesDigest": "4zshabV7Aq0g9D3qCVeRGb1a1R3O4A45WVFc6HmbuAI=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"local_config_apple_cc": {"bzlFile": "@@apple_support~//crosstool:setup.bzl", "ruleClassName": "_apple_cc_autoconf", "attributes": {}}, "local_config_apple_cc_toolchains": {"bzlFile": "@@apple_support~//crosstool:setup.bzl", "ruleClassName": "_apple_cc_autoconf_toolchains", "attributes": {}}}, "recordedRepoMappingEntries": [["apple_support~", "bazel_tools", "bazel_tools"]]}}, "@@aspect_bazel_lib~//lib:extensions.bzl%toolchains": {"general": {"bzlTransitiveDigest": "o+c8qRBWojmxp6XnxraB8cDLR3egI6E7hjYq4t1rzM8=", "usagesDigest": "osRcSZojuzZfmaRZ5K6LtDq+tCKODVarFGVKxuRWYeE=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"expand_template_windows_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:expand_template_toolchain.bzl", "ruleClassName": "expand_template_platform_repo", "attributes": {"platform": "windows_amd64"}}, "copy_to_directory_windows_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:copy_to_directory_toolchain.bzl", "ruleClassName": "copy_to_directory_platform_repo", "attributes": {"platform": "windows_amd64"}}, "jq_darwin_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:jq_toolchain.bzl", "ruleClassName": "jq_platform_repo", "attributes": {"platform": "darwin_amd64", "version": "1.7"}}, "copy_to_directory_freebsd_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:copy_to_directory_toolchain.bzl", "ruleClassName": "copy_to_directory_platform_repo", "attributes": {"platform": "freebsd_amd64"}}, "expand_template_linux_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:expand_template_toolchain.bzl", "ruleClassName": "expand_template_platform_repo", "attributes": {"platform": "linux_amd64"}}, "jq_linux_arm64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:jq_toolchain.bzl", "ruleClassName": "jq_platform_repo", "attributes": {"platform": "linux_arm64", "version": "1.7"}}, "coreutils_darwin_arm64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:coreutils_toolchain.bzl", "ruleClassName": "coreutils_platform_repo", "attributes": {"platform": "darwin_arm64", "version": "0.0.26"}}, "copy_to_directory_linux_arm64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:copy_to_directory_toolchain.bzl", "ruleClassName": "copy_to_directory_platform_repo", "attributes": {"platform": "linux_arm64"}}, "bsd_tar_linux_arm64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:tar_toolchain.bzl", "ruleClassName": "bsdtar_binary_repo", "attributes": {"platform": "linux_arm64"}}, "copy_directory_darwin_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:copy_directory_toolchain.bzl", "ruleClassName": "copy_directory_platform_repo", "attributes": {"platform": "darwin_amd64"}}, "coreutils_darwin_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:coreutils_toolchain.bzl", "ruleClassName": "coreutils_platform_repo", "attributes": {"platform": "darwin_amd64", "version": "0.0.26"}}, "coreutils_linux_arm64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:coreutils_toolchain.bzl", "ruleClassName": "coreutils_platform_repo", "attributes": {"platform": "linux_arm64", "version": "0.0.26"}}, "zstd_linux_arm64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:zstd_toolchain.bzl", "ruleClassName": "zstd_binary_repo", "attributes": {"platform": "linux_arm64"}}, "yq_linux_s390x": {"bzlFile": "@@aspect_bazel_lib~//lib/private:yq_toolchain.bzl", "ruleClassName": "yq_platform_repo", "attributes": {"platform": "linux_s390x", "version": "4.25.2"}}, "yq": {"bzlFile": "@@aspect_bazel_lib~//lib/private:yq_toolchain.bzl", "ruleClassName": "yq_host_alias_repo", "attributes": {}}, "expand_template_darwin_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:expand_template_toolchain.bzl", "ruleClassName": "expand_template_platform_repo", "attributes": {"platform": "darwin_amd64"}}, "copy_directory_linux_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:copy_directory_toolchain.bzl", "ruleClassName": "copy_directory_platform_repo", "attributes": {"platform": "linux_amd64"}}, "jq_darwin_arm64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:jq_toolchain.bzl", "ruleClassName": "jq_platform_repo", "attributes": {"platform": "darwin_arm64", "version": "1.7"}}, "yq_darwin_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:yq_toolchain.bzl", "ruleClassName": "yq_platform_repo", "attributes": {"platform": "darwin_amd64", "version": "4.25.2"}}, "copy_directory_linux_arm64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:copy_directory_toolchain.bzl", "ruleClassName": "copy_directory_platform_repo", "attributes": {"platform": "linux_arm64"}}, "expand_template_toolchains": {"bzlFile": "@@aspect_bazel_lib~//lib/private:expand_template_toolchain.bzl", "ruleClassName": "expand_template_toolchains_repo", "attributes": {"user_repository_name": "expand_template"}}, "bats_assert": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"sha256": "98ca3b685f8b8993e48ec057565e6e2abcc541034ed5b0e81f191505682037fd", "urls": ["https://github.com/bats-core/bats-assert/archive/v2.1.0.tar.gz"], "strip_prefix": "bats-assert-2.1.0", "build_file_content": "load(\"@aspect_bazel_lib//lib:copy_to_directory.bzl\", \"copy_to_directory\")\n\ncopy_to_directory(\n    name = \"assert\",\n    hardlink = \"on\",\n    srcs = glob([\n        \"src/**\",\n        \"load.bash\",\n    ]),\n    out = \"bats-assert\",\n    visibility = [\"//visibility:public\"]\n)\n"}}, "copy_to_directory_darwin_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:copy_to_directory_toolchain.bzl", "ruleClassName": "copy_to_directory_platform_repo", "attributes": {"platform": "darwin_amd64"}}, "zstd_darwin_arm64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:zstd_toolchain.bzl", "ruleClassName": "zstd_binary_repo", "attributes": {"platform": "darwin_arm64"}}, "bsd_tar_linux_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:tar_toolchain.bzl", "ruleClassName": "bsdtar_binary_repo", "attributes": {"platform": "linux_amd64"}}, "yq_toolchains": {"bzlFile": "@@aspect_bazel_lib~//lib/private:yq_toolchain.bzl", "ruleClassName": "yq_toolchains_repo", "attributes": {"user_repository_name": "yq"}}, "zstd_linux_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:zstd_toolchain.bzl", "ruleClassName": "zstd_binary_repo", "attributes": {"platform": "linux_amd64"}}, "bats_support": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"sha256": "7815237aafeb42ddcc1b8c698fc5808026d33317d8701d5ec2396e9634e2918f", "urls": ["https://github.com/bats-core/bats-support/archive/v0.3.0.tar.gz"], "strip_prefix": "bats-support-0.3.0", "build_file_content": "load(\"@aspect_bazel_lib//lib:copy_to_directory.bzl\", \"copy_to_directory\")\n\ncopy_to_directory(\n    name = \"support\",\n    hardlink = \"on\",\n    srcs = glob([\n        \"src/**\",\n        \"load.bash\",\n    ]),\n    out = \"bats-support\",\n    visibility = [\"//visibility:public\"]\n)\n"}}, "bsd_tar_windows_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:tar_toolchain.bzl", "ruleClassName": "bsdtar_binary_repo", "attributes": {"platform": "windows_amd64"}}, "jq": {"bzlFile": "@@aspect_bazel_lib~//lib/private:jq_toolchain.bzl", "ruleClassName": "jq_host_alias_repo", "attributes": {}}, "expand_template_darwin_arm64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:expand_template_toolchain.bzl", "ruleClassName": "expand_template_platform_repo", "attributes": {"platform": "darwin_arm64"}}, "bsd_tar_darwin_arm64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:tar_toolchain.bzl", "ruleClassName": "bsdtar_binary_repo", "attributes": {"platform": "darwin_arm64"}}, "copy_to_directory_linux_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:copy_to_directory_toolchain.bzl", "ruleClassName": "copy_to_directory_platform_repo", "attributes": {"platform": "linux_amd64"}}, "coreutils_linux_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:coreutils_toolchain.bzl", "ruleClassName": "coreutils_platform_repo", "attributes": {"platform": "linux_amd64", "version": "0.0.26"}}, "copy_directory_toolchains": {"bzlFile": "@@aspect_bazel_lib~//lib/private:copy_directory_toolchain.bzl", "ruleClassName": "copy_directory_toolchains_repo", "attributes": {"user_repository_name": "copy_directory"}}, "yq_linux_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:yq_toolchain.bzl", "ruleClassName": "yq_platform_repo", "attributes": {"platform": "linux_amd64", "version": "4.25.2"}}, "copy_to_directory_darwin_arm64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:copy_to_directory_toolchain.bzl", "ruleClassName": "copy_to_directory_platform_repo", "attributes": {"platform": "darwin_arm64"}}, "coreutils_toolchains": {"bzlFile": "@@aspect_bazel_lib~//lib/private:coreutils_toolchain.bzl", "ruleClassName": "coreutils_toolchains_repo", "attributes": {"user_repository_name": "coreutils"}}, "copy_directory_freebsd_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:copy_directory_toolchain.bzl", "ruleClassName": "copy_directory_platform_repo", "attributes": {"platform": "freebsd_amd64"}}, "zstd_darwin_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:zstd_toolchain.bzl", "ruleClassName": "zstd_binary_repo", "attributes": {"platform": "darwin_amd64"}}, "zstd_toolchains": {"bzlFile": "@@aspect_bazel_lib~//lib/private:zstd_toolchain.bzl", "ruleClassName": "zstd_toolchains_repo", "attributes": {"user_repository_name": "zstd"}}, "bats_file": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"sha256": "9b69043241f3af1c2d251f89b4fcafa5df3f05e97b89db18d7c9bdf5731bb27a", "urls": ["https://github.com/bats-core/bats-file/archive/v0.4.0.tar.gz"], "strip_prefix": "bats-file-0.4.0", "build_file_content": "load(\"@aspect_bazel_lib//lib:copy_to_directory.bzl\", \"copy_to_directory\")\n\ncopy_to_directory(\n    name = \"file\",\n    hardlink = \"on\",\n    srcs = glob([\n        \"src/**\",\n        \"load.bash\",\n    ]),\n    out = \"bats-file\",\n    visibility = [\"//visibility:public\"]\n)\n"}}, "expand_template_linux_arm64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:expand_template_toolchain.bzl", "ruleClassName": "expand_template_platform_repo", "attributes": {"platform": "linux_arm64"}}, "jq_linux_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:jq_toolchain.bzl", "ruleClassName": "jq_platform_repo", "attributes": {"platform": "linux_amd64", "version": "1.7"}}, "bsd_tar_darwin_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:tar_toolchain.bzl", "ruleClassName": "bsdtar_binary_repo", "attributes": {"platform": "darwin_amd64"}}, "bsd_tar_toolchains": {"bzlFile": "@@aspect_bazel_lib~//lib/private:tar_toolchain.bzl", "ruleClassName": "tar_toolchains_repo", "attributes": {"user_repository_name": "bsd_tar"}}, "bats_toolchains": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"sha256": "a1a9f7875aa4b6a9480ca384d5865f1ccf1b0b1faead6b47aa47d79709a5c5fd", "urls": ["https://github.com/bats-core/bats-core/archive/v1.10.0.tar.gz"], "strip_prefix": "bats-core-1.10.0", "build_file_content": "load(\"@local_config_platform//:constraints.bzl\", \"HOST_CONSTRAINTS\")\nload(\"@aspect_bazel_lib//lib/private:bats_toolchain.bzl\", \"bats_toolchain\")\nload(\"@aspect_bazel_lib//lib:copy_to_directory.bzl\", \"copy_to_directory\")\n\ncopy_to_directory(\n    name = \"core\",\n    hardlink = \"on\",\n    srcs = glob([\n        \"lib/**\",\n        \"libexec/**\"\n    ]) + [\"bin/bats\"],\n    out = \"bats-core\",\n)\n\nbats_toolchain(\n    name = \"toolchain\",\n    core = \":core\",\n    libraries = [\"@bats_support//:support\", \"@bats_assert//:assert\", \"@bats_file//:file\"]\n)\n\ntoolchain(\n    name = \"bats_toolchain\",\n    exec_compatible_with = HOST_CONSTRAINTS,\n    toolchain = \":toolchain\",\n    toolchain_type = \"@aspect_bazel_lib//lib:bats_toolchain_type\",\n)\n"}}, "yq_windows_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:yq_toolchain.bzl", "ruleClassName": "yq_platform_repo", "attributes": {"platform": "windows_amd64", "version": "4.25.2"}}, "jq_windows_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:jq_toolchain.bzl", "ruleClassName": "jq_platform_repo", "attributes": {"platform": "windows_amd64", "version": "1.7"}}, "expand_template_freebsd_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:expand_template_toolchain.bzl", "ruleClassName": "expand_template_platform_repo", "attributes": {"platform": "freebsd_amd64"}}, "yq_linux_ppc64le": {"bzlFile": "@@aspect_bazel_lib~//lib/private:yq_toolchain.bzl", "ruleClassName": "yq_platform_repo", "attributes": {"platform": "linux_ppc64le", "version": "4.25.2"}}, "copy_to_directory_toolchains": {"bzlFile": "@@aspect_bazel_lib~//lib/private:copy_to_directory_toolchain.bzl", "ruleClassName": "copy_to_directory_toolchains_repo", "attributes": {"user_repository_name": "copy_to_directory"}}, "jq_toolchains": {"bzlFile": "@@aspect_bazel_lib~//lib/private:jq_toolchain.bzl", "ruleClassName": "jq_toolchains_repo", "attributes": {"user_repository_name": "jq"}}, "copy_directory_darwin_arm64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:copy_directory_toolchain.bzl", "ruleClassName": "copy_directory_platform_repo", "attributes": {"platform": "darwin_arm64"}}, "copy_directory_windows_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:copy_directory_toolchain.bzl", "ruleClassName": "copy_directory_platform_repo", "attributes": {"platform": "windows_amd64"}}, "yq_darwin_arm64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:yq_toolchain.bzl", "ruleClassName": "yq_platform_repo", "attributes": {"platform": "darwin_arm64", "version": "4.25.2"}}, "coreutils_windows_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:coreutils_toolchain.bzl", "ruleClassName": "coreutils_platform_repo", "attributes": {"platform": "windows_amd64", "version": "0.0.26"}}, "yq_linux_arm64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:yq_toolchain.bzl", "ruleClassName": "yq_platform_repo", "attributes": {"platform": "linux_arm64", "version": "4.25.2"}}}, "recordedRepoMappingEntries": [["aspect_bazel_lib~", "aspect_bazel_lib", "aspect_bazel_lib~"], ["aspect_bazel_lib~", "bazel_skylib", "bazel_skylib~"], ["aspect_bazel_lib~", "bazel_tools", "bazel_tools"]]}}, "@@container_structure_test~//:repositories.bzl%extension": {"general": {"bzlTransitiveDigest": "N3vZqw3OlfuboMVlxmTq4XDxBdvPu2kNIEGbsKnRL2A=", "usagesDigest": "8Re/szfCZsgF7dlS0hgOQ+7V94maildX0111ckmLtnQ=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"structure_test_st_linux_amd64": {"bzlFile": "@@container_structure_test~//:repositories.bzl", "ruleClassName": "structure_test_repositories", "attributes": {"platform": "linux_amd64"}}, "jq_linux_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:jq_toolchain.bzl", "ruleClassName": "jq_platform_repo", "attributes": {"platform": "linux_amd64", "version": "1.7"}}, "jq": {"bzlFile": "@@aspect_bazel_lib~//lib/private:jq_toolchain.bzl", "ruleClassName": "jq_host_alias_repo", "attributes": {}}, "jq_darwin_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:jq_toolchain.bzl", "ruleClassName": "jq_platform_repo", "attributes": {"platform": "darwin_amd64", "version": "1.7"}}, "structure_test_st_windows_amd64": {"bzlFile": "@@container_structure_test~//:repositories.bzl", "ruleClassName": "structure_test_repositories", "attributes": {"platform": "windows_amd64"}}, "structure_test_toolchains": {"bzlFile": "@@container_structure_test~//bazel:toolchains_repo.bzl", "ruleClassName": "toolchains_repo", "attributes": {"toolchain_type": "@container_structure_test//bazel:structure_test_toolchain_type", "toolchain": "@structure_test_st_{platform}//:structure_test_toolchain"}}, "jq_linux_arm64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:jq_toolchain.bzl", "ruleClassName": "jq_platform_repo", "attributes": {"platform": "linux_arm64", "version": "1.7"}}, "jq_windows_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:jq_toolchain.bzl", "ruleClassName": "jq_platform_repo", "attributes": {"platform": "windows_amd64", "version": "1.7"}}, "jq_toolchains": {"bzlFile": "@@aspect_bazel_lib~//lib/private:jq_toolchain.bzl", "ruleClassName": "jq_toolchains_repo", "attributes": {"user_repository_name": "jq"}}, "structure_test_st_linux_s390x": {"bzlFile": "@@container_structure_test~//:repositories.bzl", "ruleClassName": "structure_test_repositories", "attributes": {"platform": "linux_s390x"}}, "structure_test_st_darwin_arm64": {"bzlFile": "@@container_structure_test~//:repositories.bzl", "ruleClassName": "structure_test_repositories", "attributes": {"platform": "darwin_arm64"}}, "jq_darwin_arm64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:jq_toolchain.bzl", "ruleClassName": "jq_platform_repo", "attributes": {"platform": "darwin_arm64", "version": "1.7"}}, "structure_test_st_darwin_amd64": {"bzlFile": "@@container_structure_test~//:repositories.bzl", "ruleClassName": "structure_test_repositories", "attributes": {"platform": "darwin_amd64"}}, "structure_test_st_linux_arm64": {"bzlFile": "@@container_structure_test~//:repositories.bzl", "ruleClassName": "structure_test_repositories", "attributes": {"platform": "linux_arm64"}}}, "recordedRepoMappingEntries": [["aspect_bazel_lib~", "bazel_tools", "bazel_tools"], ["container_structure_test~", "aspect_bazel_lib", "aspect_bazel_lib~"]]}}, "@@platforms//host:extension.bzl%host_platform": {"general": {"bzlTransitiveDigest": "xelQcPZH8+tmuOHVjL9vDxMnnQNMlwj0SlvgoqBkm4U=", "usagesDigest": "A8bF7CRWUcuxdQ3qv9Q7JKhmsYlg45Pu8TrYwK9JW3Q=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"host_platform": {"bzlFile": "@@platforms//host:extension.bzl", "ruleClassName": "host_platform_repo", "attributes": {}}}, "recordedRepoMappingEntries": []}}, "@@rules_kustomize~//kustomize:extensions.bzl%kustomize": {"general": {"bzlTransitiveDigest": "EpY3DkJYB5SK4BRJPD/Rm9vXoh4td7DH+5vmGshL2vw=", "usagesDigest": "3KxdAVUQvKyhdOXWOimvISgqSDlhU6xfEBxGJEH+JNo=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"kustomize_tool": {"bzlFile": "@@rules_kustomize~//kustomize/private/tools/kustomize:toolchain.bzl", "ruleClassName": "_download_tool", "attributes": {"version": "v5.4.3"}}, "kustomize_tool_toolchains": {"bzlFile": "@@rules_kustomize~//kustomize/private/tools/kustomize:toolchain.bzl", "ruleClassName": "_toolchains_repo", "attributes": {"tool_repo": "kustomize_tool", "version": "v5.4.3"}}}, "recordedRepoMappingEntries": []}}, "@@rules_oci~//oci:extensions.bzl%oci": {"general": {"bzlTransitiveDigest": "kb+/sXT2w8THjLkcvD17xA39/QzFy7CIhkgpUV6+JxE=", "usagesDigest": "LJP9whwmQWQjoNEzhb06Z40W5XBDeEhjdAkK9JZ9kUI=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"bazel_features_version": {"bzlFile": "@@bazel_features~//private:version_repo.bzl", "ruleClassName": "version_repo", "attributes": {}}, "base-os-runtime-nettools-linux-amd64": {"bzlFile": "@@rules_oci~//oci/private:pull.bzl", "ruleClassName": "oci_alias", "attributes": {"target_name": "base-os-runtime-nettools-linux-amd64", "scheme": "https", "registry": "mcr.microsoft.com", "repository": "aks/devinfra/base-os-runtime-nettools", "identifier": "sha256:a93d0418c98cc862669ec3493d904de33fa3d21bca347b00c0d9ceedd4c0099d", "platforms": {}, "platform": "@@rules_oci~//oci:base-os-runtime-nettools-linux-amd64_single", "bzlmod_repository": "base-os-runtime-nettools-linux-amd64", "reproducible": true}}, "copy_to_directory_windows_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:copy_to_directory_toolchain.bzl", "ruleClassName": "copy_to_directory_platform_repo", "attributes": {"platform": "windows_amd64"}}, "jq_darwin_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:jq_toolchain.bzl", "ruleClassName": "jq_platform_repo", "attributes": {"platform": "darwin_amd64", "version": "1.7"}}, "copy_to_directory_freebsd_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:copy_to_directory_toolchain.bzl", "ruleClassName": "copy_to_directory_platform_repo", "attributes": {"platform": "freebsd_amd64"}}, "oci_crane_linux_arm64": {"bzlFile": "@@rules_oci~//oci:repositories.bzl", "ruleClassName": "crane_repositories", "attributes": {"platform": "linux_arm64", "crane_version": "v0.18.0"}}, "jq_linux_arm64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:jq_toolchain.bzl", "ruleClassName": "jq_platform_repo", "attributes": {"platform": "linux_arm64", "version": "1.7"}}, "coreutils_darwin_arm64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:coreutils_toolchain.bzl", "ruleClassName": "coreutils_platform_repo", "attributes": {"platform": "darwin_arm64", "version": "0.0.26"}}, "bsd_tar_linux_arm64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:tar_toolchain.bzl", "ruleClassName": "bsdtar_binary_repo", "attributes": {"platform": "linux_arm64"}}, "copy_to_directory_linux_arm64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:copy_to_directory_toolchain.bzl", "ruleClassName": "copy_to_directory_platform_repo", "attributes": {"platform": "linux_arm64"}}, "oci_regctl_toolchains": {"bzlFile": "@@rules_oci~//oci/private:toolchains_repo.bzl", "ruleClassName": "toolchains_repo", "attributes": {"toolchain_type": "@rules_oci//oci:regctl_toolchain_type", "toolchain": "@oci_regctl_{platform}//:regctl_toolchain"}}, "oci_regctl_windows_armv6": {"bzlFile": "@@rules_oci~//oci:repositories.bzl", "ruleClassName": "regctl_repositories", "attributes": {"platform": "windows_armv6"}}, "oci_crane_linux_amd64": {"bzlFile": "@@rules_oci~//oci:repositories.bzl", "ruleClassName": "crane_repositories", "attributes": {"platform": "linux_amd64", "crane_version": "v0.18.0"}}, "coreutils_darwin_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:coreutils_toolchain.bzl", "ruleClassName": "coreutils_platform_repo", "attributes": {"platform": "darwin_amd64", "version": "0.0.26"}}, "coreutils_linux_arm64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:coreutils_toolchain.bzl", "ruleClassName": "coreutils_platform_repo", "attributes": {"platform": "linux_arm64", "version": "0.0.26"}}, "zstd_linux_arm64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:zstd_toolchain.bzl", "ruleClassName": "zstd_binary_repo", "attributes": {"platform": "linux_arm64"}}, "oci_crane_darwin_arm64": {"bzlFile": "@@rules_oci~//oci:repositories.bzl", "ruleClassName": "crane_repositories", "attributes": {"platform": "darwin_arm64", "crane_version": "v0.18.0"}}, "jq_darwin_arm64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:jq_toolchain.bzl", "ruleClassName": "jq_platform_repo", "attributes": {"platform": "darwin_arm64", "version": "1.7"}}, "oci_regctl_linux_s390x": {"bzlFile": "@@rules_oci~//oci:repositories.bzl", "ruleClassName": "regctl_repositories", "attributes": {"platform": "linux_s390x"}}, "oci_regctl_darwin_amd64": {"bzlFile": "@@rules_oci~//oci:repositories.bzl", "ruleClassName": "regctl_repositories", "attributes": {"platform": "darwin_amd64"}}, "oci_crane_linux_i386": {"bzlFile": "@@rules_oci~//oci:repositories.bzl", "ruleClassName": "crane_repositories", "attributes": {"platform": "linux_i386", "crane_version": "v0.18.0"}}, "base-os-runtime-nettools-linux-arm64": {"bzlFile": "@@rules_oci~//oci/private:pull.bzl", "ruleClassName": "oci_alias", "attributes": {"target_name": "base-os-runtime-nettools-linux-arm64", "scheme": "https", "registry": "mcr.microsoft.com", "repository": "aks/devinfra/base-os-runtime-nettools", "identifier": "sha256:f2f3c40d72d6985d3aa8639f64f408c862b382f6c69c37ebfe1d06b929df81f6", "platforms": {}, "platform": "@@rules_oci~//oci:base-os-runtime-nettools-linux-arm64_single", "bzlmod_repository": "base-os-runtime-nettools-linux-arm64", "reproducible": true}}, "oci_regctl_windows_amd64": {"bzlFile": "@@rules_oci~//oci:repositories.bzl", "ruleClassName": "regctl_repositories", "attributes": {"platform": "windows_amd64"}}, "oci_crane_windows_armv6": {"bzlFile": "@@rules_oci~//oci:repositories.bzl", "ruleClassName": "crane_repositories", "attributes": {"platform": "windows_armv6", "crane_version": "v0.18.0"}}, "oci_crane_toolchains": {"bzlFile": "@@rules_oci~//oci/private:toolchains_repo.bzl", "ruleClassName": "toolchains_repo", "attributes": {"toolchain_type": "@rules_oci//oci:crane_toolchain_type", "toolchain": "@oci_crane_{platform}//:crane_toolchain"}}, "copy_to_directory_darwin_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:copy_to_directory_toolchain.bzl", "ruleClassName": "copy_to_directory_platform_repo", "attributes": {"platform": "darwin_amd64"}}, "zstd_darwin_arm64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:zstd_toolchain.bzl", "ruleClassName": "zstd_binary_repo", "attributes": {"platform": "darwin_arm64"}}, "bsd_tar_linux_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:tar_toolchain.bzl", "ruleClassName": "bsdtar_binary_repo", "attributes": {"platform": "linux_amd64"}}, "oci_crane_windows_amd64": {"bzlFile": "@@rules_oci~//oci:repositories.bzl", "ruleClassName": "crane_repositories", "attributes": {"platform": "windows_amd64", "crane_version": "v0.18.0"}}, "oci_regctl_linux_arm64": {"bzlFile": "@@rules_oci~//oci:repositories.bzl", "ruleClassName": "regctl_repositories", "attributes": {"platform": "linux_arm64"}}, "oci_crane_linux_s390x": {"bzlFile": "@@rules_oci~//oci:repositories.bzl", "ruleClassName": "crane_repositories", "attributes": {"platform": "linux_s390x", "crane_version": "v0.18.0"}}, "zstd_linux_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:zstd_toolchain.bzl", "ruleClassName": "zstd_binary_repo", "attributes": {"platform": "linux_amd64"}}, "oci_regctl_darwin_arm64": {"bzlFile": "@@rules_oci~//oci:repositories.bzl", "ruleClassName": "regctl_repositories", "attributes": {"platform": "darwin_arm64"}}, "bsd_tar_windows_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:tar_toolchain.bzl", "ruleClassName": "bsdtar_binary_repo", "attributes": {"platform": "windows_amd64"}}, "jq": {"bzlFile": "@@aspect_bazel_lib~//lib/private:jq_toolchain.bzl", "ruleClassName": "jq_host_alias_repo", "attributes": {}}, "oci_crane_darwin_amd64": {"bzlFile": "@@rules_oci~//oci:repositories.bzl", "ruleClassName": "crane_repositories", "attributes": {"platform": "darwin_amd64", "crane_version": "v0.18.0"}}, "bsd_tar_darwin_arm64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:tar_toolchain.bzl", "ruleClassName": "bsdtar_binary_repo", "attributes": {"platform": "darwin_arm64"}}, "copy_to_directory_linux_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:copy_to_directory_toolchain.bzl", "ruleClassName": "copy_to_directory_platform_repo", "attributes": {"platform": "linux_amd64"}}, "coreutils_linux_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:coreutils_toolchain.bzl", "ruleClassName": "coreutils_platform_repo", "attributes": {"platform": "linux_amd64", "version": "0.0.26"}}, "bazel_skylib": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"sha256": "9f38886a40548c6e96c106b752f242130ee11aaa068a56ba7e56f4511f33e4f2", "urls": ["https://mirror.bazel.build/github.com/bazelbuild/bazel-skylib/releases/download/1.6.1/bazel-skylib-1.6.1.tar.gz", "https://github.com/bazelbuild/bazel-skylib/releases/download/1.6.1/bazel-skylib-1.6.1.tar.gz"]}}, "oci_crane_linux_armv6": {"bzlFile": "@@rules_oci~//oci:repositories.bzl", "ruleClassName": "crane_repositories", "attributes": {"platform": "linux_armv6", "crane_version": "v0.18.0"}}, "copy_to_directory_darwin_arm64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:copy_to_directory_toolchain.bzl", "ruleClassName": "copy_to_directory_platform_repo", "attributes": {"platform": "darwin_arm64"}}, "coreutils_toolchains": {"bzlFile": "@@aspect_bazel_lib~//lib/private:coreutils_toolchain.bzl", "ruleClassName": "coreutils_toolchains_repo", "attributes": {"user_repository_name": "coreutils"}}, "zstd_darwin_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:zstd_toolchain.bzl", "ruleClassName": "zstd_binary_repo", "attributes": {"platform": "darwin_amd64"}}, "zstd_toolchains": {"bzlFile": "@@aspect_bazel_lib~//lib/private:zstd_toolchain.bzl", "ruleClassName": "zstd_toolchains_repo", "attributes": {"user_repository_name": "zstd"}}, "jq_linux_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:jq_toolchain.bzl", "ruleClassName": "jq_platform_repo", "attributes": {"platform": "linux_amd64", "version": "1.7"}}, "bsd_tar_darwin_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:tar_toolchain.bzl", "ruleClassName": "bsdtar_binary_repo", "attributes": {"platform": "darwin_amd64"}}, "oci_regctl_linux_amd64": {"bzlFile": "@@rules_oci~//oci:repositories.bzl", "ruleClassName": "regctl_repositories", "attributes": {"platform": "linux_amd64"}}, "bsd_tar_toolchains": {"bzlFile": "@@aspect_bazel_lib~//lib/private:tar_toolchain.bzl", "ruleClassName": "tar_toolchains_repo", "attributes": {"user_repository_name": "bsd_tar"}}, "base-os-runtime-nettools-linux-amd64_single": {"bzlFile": "@@rules_oci~//oci/private:pull.bzl", "ruleClassName": "oci_pull", "attributes": {"scheme": "https", "registry": "mcr.microsoft.com", "repository": "aks/devinfra/base-os-runtime-nettools", "identifier": "sha256:a93d0418c98cc862669ec3493d904de33fa3d21bca347b00c0d9ceedd4c0099d", "target_name": "base-os-runtime-nettools-linux-amd64_single", "bazel_tags": []}}, "jq_windows_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:jq_toolchain.bzl", "ruleClassName": "jq_platform_repo", "attributes": {"platform": "windows_amd64", "version": "1.7"}}, "oci_regctl_linux_i386": {"bzlFile": "@@rules_oci~//oci:repositories.bzl", "ruleClassName": "regctl_repositories", "attributes": {"platform": "linux_i386"}}, "jq_toolchains": {"bzlFile": "@@aspect_bazel_lib~//lib/private:jq_toolchain.bzl", "ruleClassName": "jq_toolchains_repo", "attributes": {"user_repository_name": "jq"}}, "copy_to_directory_toolchains": {"bzlFile": "@@aspect_bazel_lib~//lib/private:copy_to_directory_toolchain.bzl", "ruleClassName": "copy_to_directory_toolchains_repo", "attributes": {"user_repository_name": "copy_to_directory"}}, "base-os-runtime-nettools-linux-arm64_single": {"bzlFile": "@@rules_oci~//oci/private:pull.bzl", "ruleClassName": "oci_pull", "attributes": {"scheme": "https", "registry": "mcr.microsoft.com", "repository": "aks/devinfra/base-os-runtime-nettools", "identifier": "sha256:f2f3c40d72d6985d3aa8639f64f408c862b382f6c69c37ebfe1d06b929df81f6", "target_name": "base-os-runtime-nettools-linux-arm64_single", "bazel_tags": []}}, "oci_regctl_linux_armv6": {"bzlFile": "@@rules_oci~//oci:repositories.bzl", "ruleClassName": "regctl_repositories", "attributes": {"platform": "linux_armv6"}}, "bazel_features_globals": {"bzlFile": "@@bazel_features~//private:globals_repo.bzl", "ruleClassName": "globals_repo", "attributes": {"globals": {"RunEnvironmentInfo": "5.3.0", "DefaultInfo": "0.0.1", "__TestingOnly_NeverAvailable": "1000000000.0.0"}}}, "coreutils_windows_amd64": {"bzlFile": "@@aspect_bazel_lib~//lib/private:coreutils_toolchain.bzl", "ruleClassName": "coreutils_platform_repo", "attributes": {"platform": "windows_amd64", "version": "0.0.26"}}}, "moduleExtensionMetadata": {"explicitRootModuleDirectDeps": ["base-os-runtime-nettools-linux-amd64", "base-os-runtime-nettools-linux-arm64"], "explicitRootModuleDirectDevDeps": [], "useAllRepos": "NO", "reproducible": false}, "recordedRepoMappingEntries": [["aspect_bazel_lib~", "bazel_tools", "bazel_tools"], ["bazel_features~", "bazel_tools", "bazel_tools"], ["rules_oci~", "aspect_bazel_lib", "aspect_bazel_lib~"], ["rules_oci~", "bazel_features", "bazel_features~"], ["rules_oci~", "bazel_skylib", "bazel_skylib~"]]}}}}