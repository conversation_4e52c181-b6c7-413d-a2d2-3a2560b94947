---
name: ccp-initializer
serviceGroup: microsoft.azurekubernetesservice.ccpimages
goPrefix: go.goms.io/aks/rp/ccp/ccp-initializer
metadata:
  aksbuilder.bazelversion: "7.3.1"

archs:
- linux-amd64
- linux-arm64
embedVersionFile: true
signImage: false

binaries:
- name: ccp-initializer
  codePath: ''

images:
- baseRepo: public/aks/ccp
  name: ccp-initializer
  publishTarget: devinfraacr
  baseImage: mcr.microsoft.com/aks/devinfra/base-os-runtime-nettools:master.250519.1
  binaries:
  - name: ccp-initializer
    targetDir: "/"
