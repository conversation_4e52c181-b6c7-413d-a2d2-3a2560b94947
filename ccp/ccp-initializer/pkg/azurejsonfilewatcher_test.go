package pkg

import (
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"
)

// tempMkdir makes a temporary directory
func tempMkdir(t *testing.T) string {
	dir, err := os.MkdirTemp("", "fsnotify")
	if err != nil {
		t.Fatalf("failed to create test directory: %s", err)
	}

	return dir
}

func TestWatchAndMutateAzureJson(t *testing.T) {
	testDir := tempMkdir(t)
	defer func() {
		_ = os.RemoveAll(testDir)
	}()

	targetDir := tempMkdir(t)
	defer func() {
		_ = os.RemoveAll(targetDir)
	}()

	testFile := filepath.Join(testDir, "testfile")
	targetFile := filepath.Join(targetDir, "targetfile")

	f, err := os.Create(testFile)
	if err != nil {
		t.Fatalf("create %s failed: %s", testFile, err.Error())
	}
	_ = f.Sync()

	ajw := AzureJsonFileWatcher{
		AzureJSONInitializer: AzureJSONInitializer{},
	}

	// Start the watcher in a goroutine
	go func() {
		_ = ajw.WatchAndMutateAzureJson(testFile, targetFile, map[string]string{"data": "newData"})
	}()

	// Give the watcher sufficient time to initialize
	// This includes time for:
	// 1. Goroutine to start
	// 2. fsnotify.NewWatcher() to be called
	// 3. watcher.Add(path) to be called
	// 4. File watcher to be actively listening
	time.Sleep(500 * time.Millisecond)

	_, _ = f.WriteString("data")
	_ = f.Sync()
	_ = f.Close()

	var content []byte
	var fileExists bool

	// Wait for the target file to be created and contain the expected content
	for i := 0; i < maxRetries; i++ {
		time.Sleep(100 * time.Millisecond) // Increased sleep time for better reliability
		content, err = os.ReadFile(targetFile)
		if err != nil {
			if i == maxRetries-1 {
				t.Fatalf("failed to read %s after %d retries: %s", targetFile, maxRetries, err.Error())
			}
			t.Logf("file not ready yet, retry attempt #%d: %s", i+1, err.Error())
			continue
		}

		fileExists = true
		// Check if content contains the expected replacement
		if strings.Contains(string(content), "newData") {
			t.Logf("success: found expected content after %d attempts", i+1)
			break
		}

		if i == maxRetries-1 {
			t.Fatalf("the content '%s' doesn't contain 'newData' after %d retries", string(content), maxRetries)
		}
		t.Logf("content not ready yet (attempt #%d): %s", i+1, string(content))
	}

	if !fileExists {
		t.Fatalf("target file %s was never created", targetFile)
	}

	if !strings.Contains(string(content), "newData") {
		t.Fatalf("the content '%s' doesn't contain 'newData'", string(content))
	}
	t.Logf("final content: %s", string(content))
}

// TestMultipleReplacements tests that multiple string replacements work correctly
func TestMultipleReplacements(t *testing.T) {
	testDir := tempMkdir(t)
	defer func() {
		_ = os.RemoveAll(testDir)
	}()

	targetDir := tempMkdir(t)
	defer func() {
		_ = os.RemoveAll(targetDir)
	}()

	testFile := filepath.Join(testDir, "testfile")
	targetFile := filepath.Join(targetDir, "targetfile")

	// Create test file with content that has multiple values to replace
	f, err := os.Create(testFile)
	if err != nil {
		t.Fatalf("create %s failed: %s", testFile, err.Error())
	}
	_, _ = f.WriteString("cloud: AzurePublicCloud, env: production")
	_ = f.Sync()
	_ = f.Close()

	ajw := AzureJsonFileWatcher{
		AzureJSONInitializer: AzureJSONInitializer{},
	}

	// Test multiple replacements
	replaceValues := map[string]string{
		"AzurePublicCloud": "AzureStackCloud",
		"production":       "staging",
	}

	err = ajw.InitializeAzureJSON(testFile, targetFile, replaceValues)
	if err != nil {
		t.Fatalf("InitializeAzureJSON failed: %s", err.Error())
	}

	content, err := os.ReadFile(targetFile)
	if err != nil {
		t.Fatalf("failed to read %s: %s", targetFile, err.Error())
	}

	contentStr := string(content)
	if !strings.Contains(contentStr, "AzureStackCloud") {
		t.Fatalf("content '%s' doesn't contain 'AzureStackCloud'", contentStr)
	}
	if !strings.Contains(contentStr, "staging") {
		t.Fatalf("content '%s' doesn't contain 'staging'", contentStr)
	}
	if strings.Contains(contentStr, "AzurePublicCloud") {
		t.Fatalf("content '%s' still contains 'AzurePublicCloud'", contentStr)
	}
	if strings.Contains(contentStr, "production") {
		t.Fatalf("content '%s' still contains 'production'", contentStr)
	}

	t.Logf("multiple replacements successful: %s", contentStr)
}
