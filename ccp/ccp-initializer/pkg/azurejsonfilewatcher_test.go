package pkg

import (
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"
)

// tempMkdir makes a temporary directory
func tempMkdir(t *testing.T) string {
	dir, err := os.MkdirTemp("", "fsnotify")
	if err != nil {
		t.Fatalf("failed to create test directory: %s", err)
	}

	return dir
}

func TestWatchAndMutateAzureJson(t *testing.T) {
	testDir := tempMkdir(t)
	defer func() {
		_ = os.RemoveAll(testDir)
	}()

	targetDir := tempMkdir(t)
	defer func() {
		_ = os.RemoveAll(targetDir)
	}()

	testFile := filepath.Join(testDir, "testfile")
	targetFile := filepath.Join(targetDir, "targetfile")

	f, err := os.Create(testFile)
	if err != nil {
		t.Fatalf("create %s failed: %s", testFile, err.Error())
	}
	_ = f.Sync()

	ajw := AzureJsonFileWatcher{
		AzureJSONInitializer: AzureJSONInitializer{},
	}
	go func() {
		_ = ajw.WatchAndMutateAzureJson(testFile, targetFile, map[string]string{"data": "newData"})
	}()

	time.Sleep(50 * time.Millisecond)

	_, _ = f.WriteString("data")
	_ = f.Sync()
	_ = f.Close()

	var content []byte
	for i := 0; i < maxRetries; i++ {
		time.Sleep(50 * time.Millisecond)
		content, err = os.ReadFile(targetFile)
		if err != nil {
			if i == maxRetries-1 {
				t.Fatalf("failed to read %s: %s", targetFile, err.Error())
			}
			t.Logf("unexpected error: %s, retry attempt #%d", err.Error(), i+1)
		} else {
			break
		}
	}

	if !strings.Contains(string(content), "newData") {
		t.Fatalf("the content %s doesn't contains newData", string(content))
	}
	t.Logf("content: %s", string(content))
}
