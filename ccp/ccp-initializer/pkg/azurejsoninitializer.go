package pkg

import (
	"io/ioutil"
	"strings"

	log "github.com/sirupsen/logrus"
)

// sed 's/{{ .Values.global.CloudProvider.Config.cloud }}/AzureStackCloud/g' /etc/kubernetes/provider/azure.json > /azure/azure.json

type AzureJSONInitializer struct{}

func (aji *AzureJSONInitializer) InitializeAzureJSON(srcFile string,
	targetFile string,
	replaceValues map[string]string) error {
	logger := log.New()
	var err error
	dat, err := ioutil.ReadFile(srcFile)
	if err == nil {
		logger.Infof("read original azure json: %s successfully", srcFile)
		strContent := string(dat)
		finalContent := strContent
		for originV, targetV := range replaceValues {
			finalContent = strings.ReplaceAll(finalContent, originV, targetV)
		}
		err = ioutil.WriteFile(targetFile, []byte(finalContent), 0600)
		if err == nil {
			logger.Infof("write target azure json: %s successfully", targetFile)
		}
	}
	return err
}
